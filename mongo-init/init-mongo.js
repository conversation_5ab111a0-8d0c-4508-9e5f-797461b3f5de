// MongoDB initialization script for Facebook Clone
// This script creates the database and initial collections

db = db.getSiblingDB('facebook_clone_db');

// Create collections
db.createCollection('users');
db.createCollection('posts');
db.createCollection('comments');
db.createCollection('likes');
db.createCollection('friendships');
db.createCollection('messages');
db.createCollection('notifications');
db.createCollection('groups');
db.createCollection('pages');

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "firstName": "text", "lastName": "text" });

db.posts.createIndex({ "author": 1, "createdAt": -1 });
db.posts.createIndex({ "createdAt": -1 });
db.posts.createIndex({ "content": "text" });

db.comments.createIndex({ "post": 1, "createdAt": -1 });
db.comments.createIndex({ "author": 1 });

db.likes.createIndex({ "post": 1, "user": 1 }, { unique: true });
db.likes.createIndex({ "user": 1 });

db.friendships.createIndex({ "requester": 1, "recipient": 1 }, { unique: true });
db.friendships.createIndex({ "recipient": 1, "status": 1 });

db.messages.createIndex({ "conversation": 1, "createdAt": -1 });
db.messages.createIndex({ "sender": 1, "recipient": 1 });

db.notifications.createIndex({ "recipient": 1, "read": 1, "createdAt": -1 });

// Create admin user
db.users.insertOne({
    _id: ObjectId(),
    firstName: "Jose L",
    lastName: "Encarnacion",
    username: "admin_soloylibre",
    email: "<EMAIL>",
    password: "$2b$12$placeholder_hash", // This will be replaced by actual hash
    profilePicture: "",
    coverPhoto: "",
    bio: "Founder of SoloYLibre Web Dev - Photography and Technology enthusiast from San Jose de Ocoa, Dom. Rep.",
    location: "New York, USA",
    website: "https://soloylibre.com",
    phone: "************",
    isVerified: true,
    role: "admin",
    createdAt: new Date(),
    updatedAt: new Date()
});

print("Facebook Clone database initialized successfully!");
print("Collections created with proper indexes");
print("Admin user created: <EMAIL>");
