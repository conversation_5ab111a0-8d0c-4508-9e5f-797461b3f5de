services:
  # WordPress Database
  wordpress_db:
    container_name: SOLOYLIBRE-SOCIAL-WP-DB
    image: mariadb:10.11
    volumes:
      - /volume1/docker/soloylibre-social/wordpress-db:/var/lib/mysql:rw
    environment:
      MYSQL_ROOT_PASSWORD: SoloYLibre2024!
      MYSQL_DATABASE: soloylibre_social_wp
      MYSQL_USER: josetusabe12
      MYSQL_PASSWORD: SoloYLibre2024!
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    ports:
      - 3306:3306
    restart: unless-stopped

  # WordPress with Social Features
  wordpress:
    container_name: SOLOYLIBRE-SOCIAL-WORDPRESS
    image: wordpress:6.4-php8.2-apache
    volumes:
      - /volume1/docker/soloylibre-social/wordpress:/var/www/html:rw
      - /volume1/docker/soloylibre-social/uploads:/var/www/html/wp-content/uploads:rw
    environment:
      # Database Connection
      WORDPRESS_DB_HOST: wordpress_db
      WORDPRESS_DB_NAME: soloylibre_social_wp
      WORDPRESS_DB_USER: josetusabe12
      WORDPRESS_DB_PASSWORD: SoloYLibre2024!
      
      # WordPress Configuration
      WORDPRESS_TABLE_PREFIX: wp_soloylibre_
      WORDPRESS_DEBUG: 0
      
      # Site Configuration
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_HOME', 'http://*************:3000');
        define('WP_SITEURL', 'http://*************:3000');
        define('FORCE_SSL_ADMIN', false);
        define('WP_MEMORY_LIMIT', '512M');
        define('WP_MAX_MEMORY_LIMIT', '512M');
        
        // Company Information
        define('COMPANY_NAME', 'SoloYLibre Web Dev');
        define('COMPANY_FOUNDER', 'Jose L Encarnacion');
        define('COMPANY_EMAIL', '<EMAIL>');
        define('COMPANY_PHONE', '************');
        define('COMPANY_LOCATION', 'San Jose de Ocoa, Dom. Rep. (Now in USA)');
        define('COMPANY_WEBSITES', 'josetusabe.com,soloylibre.com,1and1photo.com,joselencarnacion.com');
        
        // Social Network Features
        define('ENABLE_SOCIAL_FEATURES', true);
        define('ENABLE_USER_REGISTRATION', true);
        define('ENABLE_SOCIAL_LOGIN', true);
        define('ENABLE_ACTIVITY_STREAM', true);
        define('ENABLE_USER_PROFILES', true);
        define('ENABLE_MESSAGING', true);
        define('ENABLE_GROUPS', true);
        define('ENABLE_FORUMS', true);
        
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/wp-admin/admin-ajax.php"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    ports:
      - 3000:80
    restart: unless-stopped
    depends_on:
      wordpress_db:
        condition: service_healthy

  # Redis Cache for WordPress
  redis:
    container_name: SOLOYLIBRE-SOCIAL-WP-REDIS
    image: redis:7-alpine
    volumes:
      - /volume1/docker/soloylibre-social/redis:/data:rw
    command: redis-server --requirepass SoloYLibre2024! --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    ports:
      - 6379:6379
    restart: unless-stopped

  # phpMyAdmin for Database Management
  phpmyadmin:
    container_name: SOLOYLIBRE-SOCIAL-WP-PHPMYADMIN
    image: phpmyadmin:latest
    environment:
      PMA_HOST: wordpress_db
      PMA_USER: josetusabe12
      PMA_PASSWORD: SoloYLibre2024!
      MYSQL_ROOT_PASSWORD: SoloYLibre2024!
      PMA_ABSOLUTE_URI: http://*************:8080/
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ports:
      - 8080:80
    restart: unless-stopped
    depends_on:
      wordpress_db:
        condition: service_healthy

  # File Storage (MinIO)
  minio:
    container_name: SOLOYLIBRE-SOCIAL-WP-STORAGE
    image: minio/minio:latest
    volumes:
      - /volume1/docker/soloylibre-social/minio:/data:rw
    environment:
      MINIO_ROOT_USER: josetusabe12
      MINIO_ROOT_PASSWORD: SoloYLibre2024!
      MINIO_BROWSER_REDIRECT_URL: http://*************:9001
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s
    ports:
      - 9000:9000
      - 9001:9001
    restart: unless-stopped

  # Elasticsearch for Advanced Search
  elasticsearch:
    container_name: SOLOYLIBRE-SOCIAL-WP-SEARCH
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    volumes:
      - /volume1/docker/soloylibre-social/elasticsearch:/usr/share/elasticsearch/data:rw
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - cluster.name=soloylibre-social
      - node.name=soloylibre-node-1
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    ports:
      - 9200:9200
    restart: unless-stopped

  # WordPress CLI for Setup and Maintenance
  wp_cli:
    container_name: SOLOYLIBRE-SOCIAL-WP-CLI
    image: wordpress:cli-php8.2
    volumes:
      - /volume1/docker/soloylibre-social/wordpress:/var/www/html:rw
      - /volume1/docker/soloylibre-social/wp-cli:/home/<USER>
    environment:
      WORDPRESS_DB_HOST: wordpress_db
      WORDPRESS_DB_NAME: soloylibre_social_wp
      WORDPRESS_DB_USER: josetusabe12
      WORDPRESS_DB_PASSWORD: SoloYLibre2024!
    command: tail -f /dev/null
    restart: unless-stopped
    depends_on:
      wordpress:
        condition: service_healthy

  # Backup Service
  backup:
    container_name: SOLOYLIBRE-SOCIAL-WP-BACKUP
    image: databack/mysql-backup:latest
    volumes:
      - /volume1/docker/soloylibre-social/backups:/db:rw
    environment:
      DB_HOST: wordpress_db
      DB_USER: josetusabe12
      DB_PASS: SoloYLibre2024!
      DB_NAMES: soloylibre_social_wp
      DB_DUMP_FREQ: 1440  # Daily backups
      DB_DUMP_BEGIN: 0300  # 3 AM
      DB_CLEANUP_TIME: 2160  # Keep 3 days
      COMPRESSION: gzip
    restart: unless-stopped
    depends_on:
      wordpress_db:
        condition: service_healthy
