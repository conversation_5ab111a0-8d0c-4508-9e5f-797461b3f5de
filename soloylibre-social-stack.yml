services:
  # Database
  db:
    container_name: SOLOYLIBRE-SOCIAL-DATABASE
    image: postgres:15-alpine
    volumes:
      - /volume1/docker/soloylibre-social/postgres:/var/lib/postgresql/data:rw
    environment:
      POSTGRES_DB: soloylibre_social
      POSTGRES_USER: josetusabe12
      POSTGRES_PASSWORD: SoloYLibre2024!
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U josetusabe12"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    ports:
      - 5432:5432
    restart: unless-stopped

  # Redis Cache
  redis:
    container_name: SOLOYLIBRE-SOCIAL-REDIS
    image: redis:7-alpine
    volumes:
      - /volume1/docker/soloylibre-social/redis:/data:rw
    command: redis-server --requirepass SoloYLibre2024!
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    ports:
      - 6379:6379
    restart: unless-stopped

  # Discourse Forum/Social Platform
  discourse:
    container_name: SOLOYLIBRE-SOCIAL-PLATFORM
    image: discourse/discourse:latest
    volumes:
      - /volume1/docker/soloylibre-social/discourse/shared:/shared:rw
      - /volume1/docker/soloylibre-social/discourse/log:/var/log:rw
    environment:
      # Basic Settings
      DISCOURSE_HOSTNAME: social.soloylibre.com
      DISCOURSE_DEVELOPER_EMAILS: <EMAIL>
      DISCOURSE_SMTP_ADDRESS: smtp.gmail.com
      DISCOURSE_SMTP_PORT: 587
      DISCOURSE_SMTP_USER_NAME: <EMAIL>
      DISCOURSE_SMTP_PASSWORD: your_email_password
      DISCOURSE_SMTP_ENABLE_START_TLS: true
      
      # Database
      DISCOURSE_DB_HOST: db
      DISCOURSE_DB_NAME: soloylibre_social
      DISCOURSE_DB_USERNAME: josetusabe12
      DISCOURSE_DB_PASSWORD: SoloYLibre2024!
      
      # Redis
      DISCOURSE_REDIS_HOST: redis
      DISCOURSE_REDIS_PASSWORD: SoloYLibre2024!
      
      # Site Configuration
      DISCOURSE_SITE_NAME: SoloYLibre Social Network
      DISCOURSE_SITE_DESCRIPTION: Red Social de SoloYLibre Web Dev - Conectando desde San Jose de Ocoa, Dom. Rep.
      DISCOURSE_CONTACT_EMAIL: <EMAIL>
      DISCOURSE_NOTIFICATION_EMAIL: <EMAIL>
      
      # Admin User
      DISCOURSE_ADMIN_EMAIL: <EMAIL>
      DISCOURSE_ADMIN_USERNAME: josetusabe12
      DISCOURSE_ADMIN_NAME: Jose L Encarnacion
      
      # Company Info
      DISCOURSE_COMPANY_NAME: SoloYLibre Web Dev
      DISCOURSE_COMPANY_FULL_NAME: SoloYLibre Web Development
      DISCOURSE_COMPANY_DOMAIN: soloylibre.com
      
      # Features
      DISCOURSE_ENABLE_CORS: true
      DISCOURSE_ENABLE_ADMIN_API: true
      DISCOURSE_FORCE_HTTPS: false
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/srv/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    ports:
      - 3000:80
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Alternative: Simple Social Network (Friendica)
  friendica:
    container_name: SOLOYLIBRE-FRIENDICA
    image: friendica:latest
    volumes:
      - /volume1/docker/soloylibre-social/friendica:/var/www/html:rw
    environment:
      # Database
      MYSQL_HOST: friendica_db
      MYSQL_DATABASE: friendica
      MYSQL_USER: josetusabe12
      MYSQL_PASSWORD: SoloYLibre2024!
      
      # Site
      FRIENDICA_URL: http://*************:8080
      FRIENDICA_SITENAME: SoloYLibre Social Network
      FRIENDICA_ADMIN_MAIL: <EMAIL>
      FRIENDICA_ADMIN_NICK: josetusabe12
      
      # Company
      FRIENDICA_COMPANY: SoloYLibre Web Dev
      FRIENDICA_FOUNDER: Jose L Encarnacion
      FRIENDICA_LOCATION: San Jose de Ocoa, Dom. Rep.
      FRIENDICA_PHONE: ************
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    ports:
      - 8080:80
    restart: unless-stopped
    depends_on:
      - friendica_db

  # Friendica Database
  friendica_db:
    container_name: SOLOYLIBRE-FRIENDICA-DB
    image: mariadb:10.11
    volumes:
      - /volume1/docker/soloylibre-social/friendica-db:/var/lib/mysql:rw
    environment:
      MYSQL_ROOT_PASSWORD: SoloYLibre2024!
      MYSQL_DATABASE: friendica
      MYSQL_USER: josetusabe12
      MYSQL_PASSWORD: SoloYLibre2024!
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped

  # Database Admin
  phpmyadmin:
    container_name: SOLOYLIBRE-SOCIAL-PHPMYADMIN
    image: phpmyadmin:latest
    environment:
      PMA_HOST: friendica_db
      PMA_USER: josetusabe12
      PMA_PASSWORD: SoloYLibre2024!
      MYSQL_ROOT_PASSWORD: SoloYLibre2024!
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ports:
      - 8081:80
    restart: unless-stopped
    depends_on:
      - friendica_db

  # File Storage
  minio:
    container_name: SOLOYLIBRE-SOCIAL-STORAGE
    image: minio/minio:latest
    volumes:
      - /volume1/docker/soloylibre-social/minio:/data:rw
    environment:
      MINIO_ROOT_USER: josetusabe12
      MINIO_ROOT_PASSWORD: SoloYLibre2024!
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s
    ports:
      - 9000:9000
      - 9001:9001
    restart: unless-stopped

  # Monitoring
  portainer_agent:
    container_name: SOLOYLIBRE-SOCIAL-AGENT
    image: portainer/agent:latest
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:rw
      - /var/lib/docker/volumes:/var/lib/docker/volumes:rw
    ports:
      - 9001:9001
    restart: unless-stopped
