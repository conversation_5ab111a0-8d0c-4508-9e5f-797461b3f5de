services:
  # MongoDB Database
  mongodb:
    container_name: FACEBOOK-CLONE-DB
    image: mongo:7.0
    volumes:
      - /volume1/docker/facebook-clone/mongodb:/data/db:rw
    environment:
      MONGO_INITDB_ROOT_USERNAME: josetusabe12
      MONGO_INITDB_ROOT_PASSWORD: SoloYLibre2024!
      MONGO_INITDB_DATABASE: soloylibre_social
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    ports:
      - 27017:27017
    restart: unless-stopped

  # Redis Cache
  redis:
    container_name: FACEBOOK-CLONE-REDIS
    image: redis:7.2-alpine
    volumes:
      - /volume1/docker/facebook-clone/redis:/data:rw
    command: redis-server --requirepass SoloYLibre2024!
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    ports:
      - 6379:6379
    restart: unless-stopped

  # Social Media Backend (Ready-to-use)
  backend:
    container_name: FACEBOOK-CLONE-API
    image: socialengine/backend:latest
    volumes:
      - /volume1/docker/facebook-clone/backend/uploads:/app/uploads:rw
      - /volume1/docker/facebook-clone/backend/logs:/app/logs:rw
      - /volume1/docker/facebook-clone/backend/config:/app/config:rw
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: mongodb://josetusabe12:SoloYLibre2024!@mongodb:27017/soloylibre_social?authSource=admin
      REDIS_URL: redis://:SoloYLibre2024!@redis:6379
      JWT_SECRET: JeykoAi_SoloYLibre_Secret_2024_Ultimate12
      SITE_NAME: SoloYLibre Social Network
      ADMIN_EMAIL: <EMAIL>
      ADMIN_USERNAME: josetusabe12
      ADMIN_NAME: Jose L Encarnacion
      ADMIN_PHONE: ************
      COMPANY_NAME: SoloYLibre Web Dev
      DOMAIN: social.soloylibre.com
      SERVER_IP: *************
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    ports:
      - 5000:5000
    restart: unless-stopped
    depends_on:
      - mongodb
      - redis

  # Social Media Frontend (Ready-to-use)
  frontend:
    container_name: FACEBOOK-CLONE-WEB
    image: socialengine/frontend:latest
    volumes:
      - /volume1/docker/facebook-clone/frontend/config:/app/config:rw
    environment:
      REACT_APP_API_URL: http://*************:5000/api
      REACT_APP_SOCKET_URL: http://*************:5000
      REACT_APP_SITE_NAME: SoloYLibre Social Network
      REACT_APP_COMPANY: SoloYLibre Web Dev
      REACT_APP_FOUNDER: Jose L Encarnacion (JoseTusabe)
      REACT_APP_CONTACT_EMAIL: <EMAIL>
      REACT_APP_CONTACT_PHONE: ************
      REACT_APP_LOCATION: San Jose de Ocoa, Dom. Rep. (Now in USA)
      REACT_APP_WEBSITES: josetusabe.com,soloylibre.com,1and1photo.com,joselencarnacion.com
      GENERATE_SOURCEMAP: false
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s
    ports:
      - 3000:3000
    restart: unless-stopped
    depends_on:
      - backend

  # File Storage (MinIO S3-Compatible)
  storage:
    container_name: FACEBOOK-CLONE-STORAGE
    image: minio/minio:latest
    volumes:
      - /volume1/docker/facebook-clone/storage:/data:rw
    environment:
      MINIO_ROOT_USER: josetusabe12
      MINIO_ROOT_PASSWORD: SoloYLibre2024!
      MINIO_BROWSER_REDIRECT_URL: http://*************:9001
      MINIO_SERVER_URL: http://*************:9000
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s
    ports:
      - 9000:9000
      - 9001:9001
    restart: unless-stopped

  # Database Management
  dbadmin:
    container_name: FACEBOOK-CLONE-DBADMIN
    image: mongo-express:latest
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: josetusabe12
      ME_CONFIG_MONGODB_ADMINPASSWORD: SoloYLibre2024!
      ME_CONFIG_MONGODB_URL: mongodb://josetusabe12:SoloYLibre2024!@mongodb:27017/
      ME_CONFIG_BASICAUTH_USERNAME: josetusabe12
      ME_CONFIG_BASICAUTH_PASSWORD: SoloYLibre2024!
      ME_CONFIG_SITE_BASEURL: /dbadmin/
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8081"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ports:
      - 8081:8081
    restart: unless-stopped
    depends_on:
      - mongodb

  # Nginx Reverse Proxy
  proxy:
    container_name: FACEBOOK-CLONE-PROXY
    image: nginx:alpine
    volumes:
      - /volume1/docker/facebook-clone/nginx:/etc/nginx/conf.d:rw
    ports:
      - 80:80
      - 443:443
    restart: unless-stopped
    depends_on:
      - frontend
      - backend
