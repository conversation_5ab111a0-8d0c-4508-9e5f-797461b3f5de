services:
  # MongoDB Database
  mongodb:
    container_name: FACEBOOK-CLONE-DB
    image: mongo:7.0
    volumes:
      - /volume1/docker/facebook-clone/mongodb:/data/db:rw
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin_soloylibre
      MONGO_INITDB_ROOT_PASSWORD: SoloYLibre2024!
      MONGO_INITDB_DATABASE: facebook_clone_db
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    ports:
      - 27017:27017
    restart: on-failure:5

  # Redis Cache
  redis:
    container_name: FACEBOOK-CLONE-REDIS
    image: redis:7.2-alpine
    volumes:
      - /volume1/docker/facebook-clone/redis:/data:rw
    command: redis-server --requirepass SoloYLibre2024!
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    ports:
      - 6379:6379
    restart: on-failure:5

  # Backend API
  backend:
    container_name: FACEBOOK-CLONE-API
    image: josetusabe/facebook-clone-backend:latest
    volumes:
      - /volume1/docker/facebook-clone/backend/uploads:/app/uploads:rw
      - /volume1/docker/facebook-clone/backend/logs:/app/logs:rw
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: mongodb://admin_soloylibre:SoloYLibre2024!@mongodb:27017/facebook_clone_db?authSource=admin
      REDIS_URL: redis://:SoloYLibre2024!@redis:6379
      JWT_SECRET: JeykoAi_SoloYLibre_Secret_2024_Facebook_Clone
      SITE_NAME: SoloYLibre Social
      ADMIN_EMAIL: <EMAIL>
      ADMIN_NAME: Jose L Encarnacion
      ADMIN_PHONE: ************
      COMPANY_NAME: SoloYLibre Web Dev
      FACEBOOK_CLONE_HOSTNAME: social.soloylibre.com
    healthcheck:
      test: timeout 10s bash -c ':> /dev/tcp/127.0.0.1/5000' || exit 1
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 60s
    ports:
      - 5000:5000
    restart: on-failure:5
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Frontend Web App
  frontend:
    container_name: FACEBOOK-CLONE-WEB
    image: josetusabe/facebook-clone-frontend:latest
    volumes:
      - /volume1/docker/facebook-clone/frontend/config:/app/config:rw
    environment:
      REACT_APP_API_URL: http://*************:5000/api
      REACT_APP_SOCKET_URL: http://*************:5000
      REACT_APP_SITE_NAME: SoloYLibre Social
      REACT_APP_COMPANY: SoloYLibre Web Dev
      REACT_APP_CONTACT_EMAIL: <EMAIL>
      REACT_APP_CONTACT_PHONE: ************
      GENERATE_SOURCEMAP: false
    healthcheck:
      test: timeout 10s bash -c ':> /dev/tcp/127.0.0.1/3000' || exit 1
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 90s
    ports:
      - 3000:3000
    restart: on-failure:5
    depends_on:
      backend:
        condition: service_healthy

  # File Storage (MinIO)
  storage:
    container_name: FACEBOOK-CLONE-STORAGE
    image: minio/minio:latest
    volumes:
      - /volume1/docker/facebook-clone/storage:/data:rw
    environment:
      MINIO_ROOT_USER: admin_soloylibre
      MINIO_ROOT_PASSWORD: SoloYLibre2024!
      MINIO_BROWSER_REDIRECT_URL: http://*************:9001
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s
    ports:
      - 9000:9000
      - 9001:9001
    restart: on-failure:5

  # Database Admin (Adminer)
  dbadmin:
    container_name: FACEBOOK-CLONE-DBADMIN
    image: adminer:latest
    environment:
      ADMINER_DEFAULT_SERVER: mongodb
      ADMINER_DESIGN: pepa-linha
    healthcheck:
      test: timeout 10s bash -c ':> /dev/tcp/127.0.0.1/8080' || exit 1
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ports:
      - 8080:8080
    restart: on-failure:5
    depends_on:
      mongodb:
        condition: service_healthy
