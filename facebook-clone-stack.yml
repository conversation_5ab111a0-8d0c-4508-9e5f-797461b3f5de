version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: facebook_clone_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin_soloylibre
      MONGO_INITDB_ROOT_PASSWORD: SoloYLibre2024!
      MONGO_INITDB_DATABASE: facebook_clone_db
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    ports:
      - "27017:27017"
    networks:
      - facebook_clone_network

  # Redis for Caching and Sessions
  redis:
    image: redis:7.2-alpine
    container_name: facebook_clone_redis
    restart: unless-stopped
    command: redis-server --requirepass SoloYLibre2024!
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - facebook_clone_network

  # Backend API (Node.js + Express)
  backend:
    image: node:18-alpine
    container_name: facebook_clone_backend
    restart: unless-stopped
    working_dir: /app
    environment:
      NODE_ENV: development
      PORT: 5000
      MONGODB_URI: mongodb://admin_soloylibre:SoloYLibre2024!@mongodb:27017/facebook_clone_db?authSource=admin
      REDIS_URL: redis://:SoloYLibre2024!@redis:6379
      JWT_SECRET: JeykoAi_SoloYLibre_Secret_2024
      CLOUDINARY_CLOUD_NAME: soloylibre
      CLOUDINARY_API_KEY: your_cloudinary_key
      CLOUDINARY_API_SECRET: your_cloudinary_secret
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: your_email_password
    volumes:
      - ./backend:/app
      - /app/node_modules
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
      - redis
    networks:
      - facebook_clone_network
    command: sh -c "npm install && npm run dev"

  # Frontend (React.js)
  frontend:
    image: node:18-alpine
    container_name: facebook_clone_frontend
    restart: unless-stopped
    working_dir: /app
    environment:
      REACT_APP_API_URL: http://192.168.1.111:5000/api
      REACT_APP_SOCKET_URL: http://192.168.1.111:5000
      GENERATE_SOURCEMAP: false
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - facebook_clone_network
    command: sh -c "npm install && npm start"

  # Socket.io Server for Real-time Features
  socket_server:
    image: node:18-alpine
    container_name: facebook_clone_socket
    restart: unless-stopped
    working_dir: /app
    environment:
      NODE_ENV: development
      PORT: 8080
      REDIS_URL: redis://:SoloYLibre2024!@redis:6379
    volumes:
      - ./socket-server:/app
      - /app/node_modules
    ports:
      - "8080:8080"
    depends_on:
      - redis
    networks:
      - facebook_clone_network
    command: sh -c "npm install && npm start"

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: facebook_clone_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - facebook_clone_network

  # MinIO for File Storage (Alternative to AWS S3)
  minio:
    image: minio/minio:latest
    container_name: facebook_clone_minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: admin_soloylibre
      MINIO_ROOT_PASSWORD: SoloYLibre2024!
    volumes:
      - minio_data:/data
    ports:
      - "9001:9000"
      - "9002:9001"
    networks:
      - facebook_clone_network
    command: server /data --console-address ":9001"

  # Elasticsearch for Search Functionality
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: facebook_clone_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - facebook_clone_network

  # Adminer for Database Management
  adminer:
    image: adminer:latest
    container_name: facebook_clone_adminer
    restart: unless-stopped
    ports:
      - "8081:8080"
    networks:
      - facebook_clone_network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  facebook_clone_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
