services:
  # Database
  database:
    container_name: SOLOYLIBRE-SOCIAL-DB
    image: postgres:15-alpine
    volumes:
      - /volume1/docker/soloylibre-social/database:/var/lib/postgresql/data:rw
    environment:
      POSTGRES_DB: soloylibre_social
      POSTGRES_USER: josetusabe12
      POSTGRES_PASSWORD: SoloYLibre2024!
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U josetusabe12 -d soloylibre_social"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    ports:
      - 5432:5432
    restart: unless-stopped

  # Redis Cache
  cache:
    container_name: SOLOYLIBRE-SOCIAL-CACHE
    image: redis:7-alpine
    volumes:
      - /volume1/docker/soloylibre-social/redis:/data:rw
    command: redis-server --requirepass SoloYLibre2024! --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    ports:
      - 6379:6379
    restart: unless-stopped

  # Social Network Platform (Mastodon-based)
  social_web:
    container_name: SOLOYLIBRE-SOCIAL-WEB
    image: tootsuite/mastodon:latest
    volumes:
      - /volume1/docker/soloylibre-social/mastodon/public/system:/mastodon/public/system:rw
      - /volume1/docker/soloylibre-social/mastodon/public/assets:/mastodon/public/assets:rw
    environment:
      # Basic Configuration
      LOCAL_DOMAIN: social.soloylibre.com
      SINGLE_USER_MODE: false
      SECRET_KEY_BASE: JeykoAi_SoloYLibre_Secret_Key_Base_2024_Ultimate12_Facebook_Clone
      OTP_SECRET: JeykoAi_SoloYLibre_OTP_Secret_2024_Ultimate12
      VAPID_PRIVATE_KEY: your_vapid_private_key_here
      VAPID_PUBLIC_KEY: your_vapid_public_key_here
      
      # Database
      DB_HOST: database
      DB_USER: josetusabe12
      DB_NAME: soloylibre_social
      DB_PASS: SoloYLibre2024!
      DB_PORT: 5432
      
      # Redis
      REDIS_HOST: cache
      REDIS_PORT: 6379
      REDIS_PASSWORD: SoloYLibre2024!
      
      # Email (Optional - configure later)
      SMTP_SERVER: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_LOGIN: <EMAIL>
      SMTP_PASSWORD: your_email_password
      SMTP_FROM_ADDRESS: <EMAIL>
      
      # Admin
      ADMIN_EMAIL: <EMAIL>
      ADMIN_USERNAME: josetusabe12
      
      # Site Info
      SITE_TITLE: SoloYLibre Social Network
      SITE_SHORT_DESCRIPTION: Red Social de SoloYLibre Web Dev - Jose L Encarnacion
      SITE_DESCRIPTION: Plataforma social desarrollada por Jose L Encarnacion (JoseTusabe) de SoloYLibre Web Dev. Conectando desde San Jose de Ocoa, Dom. Rep.
      SITE_CONTACT_USERNAME: josetusabe12
      SITE_CONTACT_EMAIL: <EMAIL>
      
      # Features
      REGISTRATIONS_MODE: open
      AUTHORIZED_FETCH: false
      
      # Performance
      WEB_CONCURRENCY: 2
      MAX_THREADS: 5
      
      # Storage
      S3_ENABLED: false
      
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider --proxy=off localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    ports:
      - 3000:3000
    restart: unless-stopped
    depends_on:
      database:
        condition: service_healthy
      cache:
        condition: service_healthy

  # Background Jobs
  social_sidekiq:
    container_name: SOLOYLIBRE-SOCIAL-JOBS
    image: tootsuite/mastodon:latest
    volumes:
      - /volume1/docker/soloylibre-social/mastodon/public/system:/mastodon/public/system:rw
    environment:
      # Same environment as social_web
      LOCAL_DOMAIN: social.soloylibre.com
      SECRET_KEY_BASE: JeykoAi_SoloYLibre_Secret_Key_Base_2024_Ultimate12_Facebook_Clone
      OTP_SECRET: JeykoAi_SoloYLibre_OTP_Secret_2024_Ultimate12
      DB_HOST: database
      DB_USER: josetusabe12
      DB_NAME: soloylibre_social
      DB_PASS: SoloYLibre2024!
      DB_PORT: 5432
      REDIS_HOST: cache
      REDIS_PORT: 6379
      REDIS_PASSWORD: SoloYLibre2024!
    command: bundle exec sidekiq
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep '[s]idekiq' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    depends_on:
      database:
        condition: service_healthy
      cache:
        condition: service_healthy

  # Streaming API
  social_streaming:
    container_name: SOLOYLIBRE-SOCIAL-STREAMING
    image: tootsuite/mastodon:latest
    environment:
      # Same environment as social_web
      LOCAL_DOMAIN: social.soloylibre.com
      SECRET_KEY_BASE: JeykoAi_SoloYLibre_Secret_Key_Base_2024_Ultimate12_Facebook_Clone
      OTP_SECRET: JeykoAi_SoloYLibre_OTP_Secret_2024_Ultimate12
      DB_HOST: database
      DB_USER: josetusabe12
      DB_NAME: soloylibre_social
      DB_PASS: SoloYLibre2024!
      DB_PORT: 5432
      REDIS_HOST: cache
      REDIS_PORT: 6379
      REDIS_PASSWORD: SoloYLibre2024!
    command: node ./streaming
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider --proxy=off localhost:4000/api/v1/streaming/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    ports:
      - 4000:4000
    restart: unless-stopped
    depends_on:
      database:
        condition: service_healthy
      cache:
        condition: service_healthy

  # Database Admin
  dbadmin:
    container_name: SOLOYLIBRE-SOCIAL-DBADMIN
    image: adminer:latest
    environment:
      ADMINER_DEFAULT_SERVER: database
      ADMINER_DESIGN: pepa-linha
    healthcheck:
      test: ["CMD", "php", "-r", "if(file_get_contents('http://localhost:8080')) exit(0); exit(1);"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ports:
      - 8080:8080
    restart: unless-stopped
    depends_on:
      - database

  # Nginx Reverse Proxy
  proxy:
    container_name: SOLOYLIBRE-SOCIAL-PROXY
    image: nginx:alpine
    volumes:
      - /volume1/docker/soloylibre-social/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - /volume1/docker/soloylibre-social/nginx/ssl:/etc/nginx/ssl:ro
    ports:
      - 80:80
      - 443:443
    restart: unless-stopped
    depends_on:
      - social_web
