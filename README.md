# 🚀 Facebook Clone - SoloYLibre Social

Un clon completo de Facebook desarrollado para deployment en Synology NAS usando Docker y Portainer.

## 👨‍💻 Desarrollado por
**<PERSON> L Encarnacion (JoseTusabe)**  
🏢 **SoloYLibre Web Dev**  
📧 <EMAIL>  
📱 ************  
🌐 josetusabe.com | soloylibre.com | 1and1photo.com | joselencarnacion.com  
📍 San Jose de Ocoa, Dom. Rep. (Actualmente en USA)

## 🏗️ Arquitectura del Stack

### Servicios Incluidos:
- **Frontend**: React.js con interfaz moderna tipo Facebook
- **Backend**: Node.js + Express API REST
- **Base de Datos**: MongoDB para datos principales
- **Cache**: Redis para sesiones y cache
- **Storage**: MinIO para archivos e imágenes
- **Admin**: Adminer para gestión de base de datos

### Puertos Utilizados:
- `3000` - Frontend Web App
- `5000` - Backend API
- `8080` - Database Admin (Adminer)
- `9000` - MinIO Storage API
- `9001` - <PERSON><PERSON> Console
- `27017` - MongoDB
- `6379` - Redis

## 🚀 Instalación en Synology

### Paso 1: Preparar el Servidor
```bash
# Ejecutar en SSH de tu Synology
chmod +x setup-facebook-clone.sh
./setup-facebook-clone.sh
```

### Paso 2: Desplegar en Portainer
1. Accede a Portainer: `http://*************:9000`
2. Ve a **Stacks** → **Add Stack**
3. Nombre del stack: `facebook-clone-soloylibre`
4. Copia el contenido de `facebook-clone-stack.yml`
5. Click en **Deploy the stack**

### Paso 3: Verificar Deployment
Espera a que todos los servicios estén **healthy** (verde en Portainer)

## 🔗 Acceso a Servicios

| Servicio | URL | Credenciales |
|----------|-----|--------------|
| **Facebook Clone** | http://*************:3000 | <EMAIL> |
| **API Backend** | http://*************:5000/api | - |
| **Database Admin** | http://*************:8080 | MongoDB |
| **File Storage** | http://*************:9001 | admin_soloylibre / SoloYLibre2024! |

## 👤 Usuario Administrador

**Email**: <EMAIL>  
**Username**: josetusabe  
**Nombre**: Jose L Encarnacion  
**Empresa**: SoloYLibre Web Dev  
**Teléfono**: ************

## 🎨 Características

### ✅ Funcionalidades Implementadas:
- [x] Sistema de autenticación y registro
- [x] Perfiles de usuario personalizables
- [x] Publicación de posts con texto e imágenes
- [x] Sistema de likes y comentarios
- [x] Sistema de amistad (enviar/aceptar solicitudes)
- [x] Chat en tiempo real
- [x] Notificaciones push
- [x] Búsqueda de usuarios y contenido
- [x] Subida de fotos de perfil y portada
- [x] Timeline personalizado
- [x] Configuración de privacidad

### 🔄 En Desarrollo:
- [ ] Stories (historias)
- [ ] Grupos y páginas
- [ ] Marketplace
- [ ] Video calls
- [ ] Live streaming
- [ ] Mobile app (React Native)

## 🛠️ Tecnologías Utilizadas

### Frontend:
- React.js 18
- Material-UI / Chakra UI
- Socket.io Client
- Axios
- React Router

### Backend:
- Node.js + Express
- MongoDB + Mongoose
- Redis
- Socket.io
- JWT Authentication
- Multer (file uploads)
- Bcrypt (password hashing)

### DevOps:
- Docker & Docker Compose
- Portainer
- Nginx (reverse proxy)
- MinIO (S3-compatible storage)

## 📊 Especificaciones del Servidor

**Synology RS3618xs**  
- **RAM**: 56GB
- **Storage**: 36TB
- **Network**: Gigabit Ethernet
- **OS**: DSM 7.x

## 🔧 Configuración Avanzada

### Variables de Entorno Personalizables:
```yaml
environment:
  SITE_NAME: "SoloYLibre Social"
  ADMIN_EMAIL: "<EMAIL>"
  ADMIN_NAME: "Jose L Encarnacion"
  COMPANY_NAME: "SoloYLibre Web Dev"
  FACEBOOK_CLONE_HOSTNAME: "social.soloylibre.com"
```

### Volúmenes de Datos:
```
/volume1/docker/facebook-clone/
├── mongodb/          # Base de datos
├── redis/            # Cache
├── backend/
│   ├── uploads/      # Archivos subidos
│   └── logs/         # Logs del sistema
├── frontend/config/  # Configuración del frontend
└── storage/          # MinIO storage
```

## 🚨 Troubleshooting

### Problemas Comunes:

1. **Servicios no inician**:
   ```bash
   docker-compose logs [service_name]
   ```

2. **Error de conexión a MongoDB**:
   - Verificar que el puerto 27017 esté disponible
   - Revisar credenciales en variables de entorno

3. **Frontend no carga**:
   - Verificar que el backend esté corriendo
   - Revisar la variable REACT_APP_API_URL

## 📞 Soporte

**Jose L Encarnacion (JoseTusabe)**  
📧 <EMAIL>  
📱 ************  
🌐 josetusabe.com | soloylibre.com

---

## 📄 Licencia

MIT License - SoloYLibre Web Dev © 2024

**Desarrollado con ❤️ por Jose L Encarnacion**  
*Photography and Technology enthusiast from San Jose de Ocoa, Dom. Rep.*
