{"name": "facebook-clone-backend", "version": "1.0.0", "description": "Backend API for Facebook Clone by SoloYLibre Web Dev", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedDatabase.js"}, "keywords": ["facebook", "clone", "social", "media", "api", "<PERSON><PERSON><PERSON><PERSON>"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "redis": "^4.6.10", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "sharp": "^0.32.6", "moment": "^2.29.4", "uuid": "^9.0.1", "express-session": "^1.17.3", "connect-redis": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}