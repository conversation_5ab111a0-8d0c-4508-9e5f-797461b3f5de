#!/bin/bash

# Facebook Clone Setup Script for Synology NAS
# Created by: <PERSON> (JoseTusabe)
# Company: SoloYLibre Web Dev
# Email: <EMAIL>
# Phone: ************

echo "🚀 Setting up Facebook Clone on Synology NAS"
echo "👨‍💻 Created by: <PERSON> (JoseTusabe)"
echo "🏢 Company: SoloYLibre Web Dev"
echo "📧 Email: <EMAIL>"
echo "📱 Phone: ************"
echo "🌐 Websites: josetusabe.com | soloylibre.com"
echo ""

# Create directory structure
echo "📁 Creating directory structure..."
sudo mkdir -p /volume1/docker/facebook-clone/{mongodb,redis,backend/{uploads,logs},frontend/config,storage}

# Set permissions
echo "🔐 Setting permissions..."
sudo chown -R 1000:1000 /volume1/docker/facebook-clone/
sudo chmod -R 755 /volume1/docker/facebook-clone/

# Create MongoDB initialization script
echo "🗄️ Creating MongoDB initialization..."
cat > /volume1/docker/facebook-clone/mongodb/init-mongo.js << 'EOF'
// Facebook Clone MongoDB Initialization
// SoloYLibre Web Dev - Jose L Encarnacion

db = db.getSiblingDB('facebook_clone_db');

// Create collections
db.createCollection('users');
db.createCollection('posts');
db.createCollection('comments');
db.createCollection('likes');
db.createCollection('friendships');
db.createCollection('messages');
db.createCollection('notifications');

// Create indexes
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.posts.createIndex({ "author": 1, "createdAt": -1 });
db.comments.createIndex({ "post": 1, "createdAt": -1 });
db.likes.createIndex({ "post": 1, "user": 1 }, { unique: true });
db.friendships.createIndex({ "requester": 1, "recipient": 1 }, { unique: true });
db.messages.createIndex({ "conversation": 1, "createdAt": -1 });
db.notifications.createIndex({ "recipient": 1, "read": 1, "createdAt": -1 });

// Create admin user
db.users.insertOne({
    firstName: "Jose L",
    lastName: "Encarnacion",
    username: "josetusabe",
    email: "<EMAIL>",
    bio: "Founder of SoloYLibre Web Dev - Photography and Technology enthusiast",
    location: "New York, USA",
    website: "https://soloylibre.com",
    phone: "************",
    isVerified: true,
    role: "admin",
    createdAt: new Date()
});

print("✅ Facebook Clone database initialized!");
EOF

# Create frontend configuration
echo "⚛️ Creating frontend configuration..."
cat > /volume1/docker/facebook-clone/frontend/config/app.json << 'EOF'
{
  "siteName": "SoloYLibre Social",
  "company": "SoloYLibre Web Dev",
  "founder": "Jose L Encarnacion",
  "contact": {
    "email": "<EMAIL>",
    "phone": "************",
    "websites": ["josetusabe.com", "soloylibre.com", "1and1photo.com", "joselencarnacion.com"]
  },
  "location": "San Jose de Ocoa, Dom. Rep. (Now in USA)",
  "theme": {
    "primaryColor": "#1877f2",
    "secondaryColor": "#42b883",
    "accentColor": "#ff6b6b"
  },
  "features": {
    "realTimeChat": true,
    "photoSharing": true,
    "videoSharing": true,
    "stories": true,
    "groups": true,
    "pages": true,
    "marketplace": false
  }
}
EOF

# Create backend logs directory structure
echo "📝 Creating backend logs structure..."
mkdir -p /volume1/docker/facebook-clone/backend/logs/{access,error,debug}

# Create uploads directory structure
echo "📸 Creating uploads structure..."
mkdir -p /volume1/docker/facebook-clone/backend/uploads/{profiles,posts,stories,messages}

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Copy the facebook-clone-stack.yml to your Portainer"
echo "2. Deploy the stack in Portainer"
echo "3. Wait for all services to be healthy"
echo "4. Access your Facebook Clone at: http://192.168.1.111:3000"
echo ""
echo "🔗 Service URLs:"
echo "   • Frontend: http://192.168.1.111:3000"
echo "   • Backend API: http://192.168.1.111:5000"
echo "   • Database Admin: http://192.168.1.111:8080"
echo "   • File Storage: http://192.168.1.111:9001"
echo ""
echo "👤 Default Admin Credentials:"
echo "   • Email: <EMAIL>"
echo "   • Username: josetusabe"
echo ""
echo "🏢 Created by SoloYLibre Web Dev"
echo "👨‍💻 Jose L Encarnacion (JoseTusabe)"
echo "📧 <EMAIL> | 📱 ************"
