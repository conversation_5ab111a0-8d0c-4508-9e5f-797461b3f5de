#!/bin/bash

# WordPress Social Network Setup Script
# SoloYLibre Web Dev - Jose L Encarnacion (JoseTusabe)
# Email: <EMAIL> | Phone: ************

echo "🚀 Setting up SoloYLibre Social Network (WordPress-based)"
echo "👨‍💻 Created by: <PERSON> (JoseTusabe)"
echo "🏢 Company: SoloYLibre Web Dev"
echo "📧 Email: <EMAIL>"
echo "📱 Phone: ************"
echo "🌐 Websites: josetusabe.com | soloylibre.com"
echo ""

# Create directory structure
echo "📁 Creating directory structure..."
sudo mkdir -p /volume1/docker/soloylibre-social/{wordpress,wordpress-db,uploads,redis,minio,elasticsearch,backups,wp-cli}

# Set permissions
echo "🔐 Setting permissions..."
sudo chown -R 33:33 /volume1/docker/soloylibre-social/wordpress
sudo chown -R 33:33 /volume1/docker/soloylibre-social/uploads
sudo chown -R 999:999 /volume1/docker/soloylibre-social/redis
sudo chown -R 1000:1000 /volume1/docker/soloylibre-social/elasticsearch
sudo chmod -R 755 /volume1/docker/soloylibre-social/

# Create WordPress configuration
echo "⚙️ Creating WordPress configuration..."
cat > /volume1/docker/soloylibre-social/wp-cli/install-plugins.sh << 'EOF'
#!/bin/bash

# Wait for WordPress to be ready
sleep 30

# Install WordPress
wp core install \
  --url="http://*************:3000" \
  --title="SoloYLibre Social Network" \
  --admin_user="josetusabe12" \
  --admin_password="SoloYLibre2024!" \
  --admin_email="<EMAIL>" \
  --skip-email

# Install and activate social plugins
echo "Installing BuddyPress (Social Network Core)..."
wp plugin install buddypress --activate

echo "Installing bbPress (Forums)..."
wp plugin install bbpress --activate

echo "Installing BuddyBoss Platform (Enhanced Social Features)..."
wp plugin install buddyboss-platform --activate

echo "Installing Ultimate Member (User Profiles)..."
wp plugin install ultimate-member --activate

echo "Installing WP User Frontend (Frontend Posting)..."
wp plugin install wp-user-frontend --activate

echo "Installing Social Login (OAuth)..."
wp plugin install nextend-social-login --activate

echo "Installing Activity Stream..."
wp plugin install buddypress-activity-plus --activate

echo "Installing Private Messaging..."
wp plugin install bp-better-messages --activate

echo "Installing Photo/Video Sharing..."
wp plugin install buddypress-media --activate

echo "Installing Events Calendar..."
wp plugin install the-events-calendar --activate

echo "Installing WooCommerce (Marketplace)..."
wp plugin install woocommerce --activate

echo "Installing Redis Cache..."
wp plugin install redis-cache --activate

echo "Installing Elasticsearch..."
wp plugin install elasticpress --activate

echo "Installing Security..."
wp plugin install wordfence --activate

echo "Installing SEO..."
wp plugin install wordpress-seo --activate

echo "Installing Backup..."
wp plugin install updraftplus --activate

# Configure BuddyPress
wp option update bp-active-components '{"activity":"1","members":"1","xprofile":"1","friends":"1","messages":"1","settings":"1","notifications":"1","groups":"1","blogs":"1","forums":"1"}'

# Configure site settings
wp option update blogname "SoloYLibre Social Network"
wp option update blogdescription "Red Social de SoloYLibre Web Dev - Conectando desde San Jose de Ocoa, Dom. Rep."
wp option update admin_email "<EMAIL>"
wp option update users_can_register 1
wp option update default_role "subscriber"

# Set company information
wp option update company_name "SoloYLibre Web Dev"
wp option update company_founder "Jose L Encarnacion"
wp option update company_email "<EMAIL>"
wp option update company_phone "************"
wp option update company_location "San Jose de Ocoa, Dom. Rep. (Now in USA)"
wp option update company_websites "josetusabe.com,soloylibre.com,1and1photo.com,joselencarnacion.com"

# Create pages
wp post create --post_type=page --post_title="Home" --post_status=publish --post_content="Welcome to SoloYLibre Social Network"
wp post create --post_type=page --post_title="Activity" --post_status=publish --post_content="[buddypress component=activity]"
wp post create --post_type=page --post_title="Members" --post_status=publish --post_content="[buddypress component=members]"
wp post create --post_type=page --post_title="Groups" --post_status=publish --post_content="[buddypress component=groups]"
wp post create --post_type=page --post_title="Forums" --post_status=publish --post_content="[bbp-forum-index]"

# Set front page
FRONT_PAGE_ID=$(wp post list --post_type=page --post_title="Home" --field=ID)
wp option update show_on_front page
wp option update page_on_front $FRONT_PAGE_ID

# Install and activate social theme
wp theme install buddyboss-theme --activate

echo "✅ WordPress Social Network setup completed!"
echo "🌐 Access your site at: http://*************:3000"
echo "👤 Admin login: josetusabe12 / SoloYLibre2024!"
EOF

chmod +x /volume1/docker/soloylibre-social/wp-cli/install-plugins.sh

# Create nginx configuration for better performance
echo "🌐 Creating Nginx configuration..."
mkdir -p /volume1/docker/soloylibre-social/nginx
cat > /volume1/docker/soloylibre-social/nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream wordpress {
        server wordpress:80;
    }

    server {
        listen 80;
        server_name social.soloylibre.com *************;

        client_max_body_size 100M;

        location / {
            proxy_pass http://wordpress;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            proxy_pass http://wordpress;
            proxy_cache_valid 200 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
EOF

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Copy wordpress-social-stack.yml to Portainer"
echo "2. Deploy the stack: 'soloylibre-social'"
echo "3. Wait for all services to start (2-3 minutes)"
echo "4. Run the plugin installation:"
echo "   docker exec SOLOYLIBRE-SOCIAL-WP-CLI /home/<USER>/install-plugins.sh"
echo ""
echo "🔗 Service URLs:"
echo "   • Social Network: http://*************:3000"
echo "   • Database Admin: http://*************:8080"
echo "   • File Storage: http://*************:9001"
echo "   • Search Engine: http://*************:9200"
echo ""
echo "👤 Admin Credentials:"
echo "   • Username: josetusabe12"
echo "   • Password: SoloYLibre2024!"
echo "   • Email: <EMAIL>"
echo ""
echo "🎯 Features Included:"
echo "   ✅ User Profiles & Registration"
echo "   ✅ Activity Streams (like Facebook timeline)"
echo "   ✅ Private Messaging"
echo "   ✅ Groups & Communities"
echo "   ✅ Forums & Discussions"
echo "   ✅ Photo/Video Sharing"
echo "   ✅ Events Calendar"
echo "   ✅ Social Login (Google, Facebook, etc.)"
echo "   ✅ Mobile Responsive"
echo "   ✅ Real-time Notifications"
echo "   ✅ Advanced Search"
echo "   ✅ Marketplace (WooCommerce)"
echo ""
echo "🏢 Created by SoloYLibre Web Dev"
echo "👨‍💻 Jose L Encarnacion (JoseTusabe)"
echo "📧 <EMAIL> | 📱 ************"
